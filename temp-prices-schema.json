{"$id": "Prices", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "provider": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "bundle": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "source": {"type": "string", "enum": ["vision", "bill", "provider", "dataset", "va", "upload", "ptf"]}, "description": {"type": "string"}, "notes": {"type": "string"}, "session": {"type": "string"}, "state": {"type": "string"}, "eraser": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "providerName": {"type": "string"}, "price": {"type": "number"}, "uom": {"type": "string"}, "batch": {"type": "string"}, "uid": {"type": "string"}, "subject": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "type": {"type": "string", "enum": ["procedures", "meds", "other"]}, "code": {"type": "string"}, "billing_code": {"type": "string"}, "carrier": {"type": "string"}, "ndcs": {"type": "array", "items": {"type": "string"}}, "relatedCheckedAt": {}, "ndc": {"type": "string"}, "ndc10": {"type": "string"}, "ndc11": {"type": "string"}, "labeler": {"type": "string"}, "product": {"type": "string"}, "package": {"type": "string"}, "rxcui": {"type": "string"}, "s_f": {"type": "string"}}}