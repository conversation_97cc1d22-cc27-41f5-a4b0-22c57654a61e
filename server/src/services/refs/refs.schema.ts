// TypeBox schema for refs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, imageSchema, phoneSchema } from '../../utils/common/typebox-schemas.js'

import { sendTo } from '../ims/schemas/index.js'

export const refsSchema = Type.Object(
    {
      // required at root
      _id: ObjectIdSchema(),
      name: Type.String(),
      person: ObjectIdSchema(),

      // optional
      avatar: Type.Optional(imageSchema),
      org: Type.Optional(ObjectIdSchema()),
      disabled: Type.Optional(Type.String()),
      disabledBy: Type.Optional(ObjectIdSchema()),
      disabledAt: Type.Optional(Type.Any()),
      isHost: Type.Optional(ObjectIdSchema()),
      showBy: Type.Optional(Type.Boolean()),
      npn: Type.Optional(Type.String()),
      sendTo: Type.Optional(sendTo),
      approved: Type.Optional(Type.Boolean()),
      approvedAt: Type.Optional(Type.Any()),
      approvedBy: Type.Optional(ObjectIdSchema()),
      leads: Type.Optional(Type.Array(ObjectIdSchema())),
      tags: Type.Optional(Type.Array(Type.String())),
      phone: Type.Optional(phoneSchema),
      email: Type.Optional(Type.String()),
      calendar: Type.Optional(ObjectIdSchema()),
      calendars: Type.Optional(Type.Array(ObjectIdSchema())),
      contract: Type.Optional(ObjectIdSchema()),
      status: Type.Optional(
          Type.String({ enum: ['invited', 'req', 'canceled', 'active'] })
      ),
      teams: Type.Optional(
          Type.Record(
              Type.String(),
              Type.Object(
                  {
                    calendar: Type.Optional(ObjectIdSchema()),
                  },
                  { additionalProperties: true }
              )
          )
      ),

      // spread in shared common fields
      ...commonFields,
    },
    {
      $id: 'Refs',
      additionalProperties: true,
      required: ['_id', 'name', 'person'],
    }
);

export type Refs = Static<typeof refsSchema>
export const refsValidator = getValidator(refsSchema, dataValidator)
export const refsResolver = resolve<Refs, HookContext>({})
export const refsExternalResolver = resolve<Refs, HookContext>({})

export const refsDataSchema = Type.Object({
  ...Type.Omit(refsSchema, ['_id']).properties
}, { additionalProperties: false })

export type RefsData = Static<typeof refsDataSchema>
export const refsDataValidator = getValidator(refsDataSchema, dataValidator)
export const refsDataResolver = resolve<RefsData, HookContext>({})

const refsQueryProperties = Type.Pick(refsSchema, ['_id', 'reference', 'createdBy', 'updatedBy'])

export const refsPatchSchema = commonPatch(refsSchema, { pushPullOpts: [], pickedForSet: refsQueryProperties })
export type RefsPatch = Static<typeof refsPatchSchema>
export const refsPatchValidator = getValidator(refsPatchSchema, dataValidator)
export const refsPatchResolver = resolve<RefsPatch, HookContext>({})



export const refsQuerySchema = queryWrapper(refsQueryProperties)
export type RefsQuery = Static<typeof refsQuerySchema>
export const refsQueryValidator = getValidator(refsQuerySchema, queryValidator)
export const refsQueryResolver = resolve<RefsQuery, HookContext>({})
