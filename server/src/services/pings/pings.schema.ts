// TypeBox schema for pings service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, queryWrapper } from '../../utils/common/typebox-schemas.js'

const sendSchema = Type.Object({
    send: Type.Optional(Type.Boolean()),
    sent: Type.Optional(Type.String()),
    unsubbed: Type.Optional(Type.Boolean()),
    error: Type.Optional(Type.String()),
    errorMessage: Type.Optional(Type.String()),
    tries: Type.Optional(Type.Number()),
    opened: Type.Optional(Type.String()),
})
export const pingsSchema = Type.Object({
  _id: ObjectIdSchema(),
  subject: Type.Optional(ObjectIdSchema()),
  subjectService: Type.Optional(Type.String()),
  recipient: Type.Optional(ObjectIdSchema()),
  recipientService: Type.Optional(Type.String()),
  nouns: Type.Optional(Type.Array(Type.String())),
  verbs: Type.Optional(Type.Array(Type.String())),
  message: Type.Optional(Type.String()),
  subjectAvatarPath: Type.Optional(Type.String()),
  subjectNamePath: Type.Optional(Type.String()),
  link: Type.Optional(Type.String()),
  tries: Type.Optional(Type.Number()),
  action: Type.Optional(Type.String()),
  priority: Type.Optional(Type.Number()),
  category: Type.Optional(Type.String()),
  methods: Type.Optional(
    Type.Object({
      internal: Type.Optional(sendSchema),
      email: Type.Optional(sendSchema),
      sms: Type.Optional(sendSchema)
    })
  ),
  ...commonFields
}, { $id: "Pings", additionalProperties: false })

export type Pings = Static<typeof pingsSchema>
export const pingsValidator = getValidator(pingsSchema, dataValidator)
export const pingsResolver = resolve<Pings, HookContext>({})
export const pingsExternalResolver = resolve<Pings, HookContext>({})

export const pingsDataSchema = Type.Object({
  ...Type.Omit(pingsSchema, ['_id']).properties
}, { additionalProperties: false })

export type PingsData = Static<typeof pingsDataSchema>
export const pingsDataValidator = getValidator(pingsDataSchema, dataValidator)
export const pingsDataResolver = resolve<PingsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const pingsQueryProperties = Type.Pick(pingsSchema, ['_id', 'subject', 'recipient', 'createdBy', 'updatedBy', 'env', 'host', 'ref', 'changeLog'])

export const pingsPatchSchema = commonPatch(pingsSchema, { pushPullOpts: [], pickedForSet: pingsQueryProperties })
export type PingsPatch = Static<typeof pingsPatchSchema>
export const pingsPatchValidator = getValidator(pingsPatchSchema, dataValidator)
export const pingsPatchResolver = resolve<PingsPatch, HookContext>({})

export const pingsQuerySchema = queryWrapper(pingsQueryProperties)
export type PingsQuery = Static<typeof pingsQuerySchema>
export const pingsQueryValidator = getValidator(pingsQuerySchema, queryValidator)
export const pingsQueryResolver = resolve<PingsQuery, HookContext>({})
