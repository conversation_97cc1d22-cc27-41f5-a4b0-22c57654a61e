// TypeBox schema for practitioners service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { queryWrapper, commonFields, imageSchema, commonPatch } from '../../utils/common/typebox-schemas.js'

// Main data model schema
export const practitionersSchema = Type.Object({
  _id: ObjectIdSchema(),
  person: Type.Optional(ObjectIdSchema()),
  avatar: Type.Optional(imageSchema),
  firstName: Type.String(),
  lastName: Type.String(),
  name: Type.Optional(Type.String()),
  name_prefix: Type.Optional(Type.String()),
  gender: Type.Optional(Type.String()),
  credential: Type.Optional(Type.String()),
  auto_created: Type.Optional(Type.Boolean()),
  credentials: Type.Optional(Type.Array(Type.String())),
  phone: Type.Optional(
    Type.Object(
      {
        number: Type.Optional(
          Type.Object({
            input: Type.Optional(Type.String()),
            international: Type.Optional(Type.String()),
            national: Type.Optional(Type.String()),
            e164: Type.Optional(Type.String()),
            rfc3966: Type.Optional(Type.String()),
            significant: Type.Optional(Type.String()),
          })
        ),
        regionCode: Type.Optional(Type.String()),
        valid: Type.Optional(Type.Boolean()),
        possible: Type.Optional(Type.Boolean()),
        possibility: Type.Optional(Type.String()),
        countryCode: Type.Optional(Type.Number()),
        canBeInternationallyDialled: Type.Optional(Type.Boolean()),
        typeIsMobile: Type.Optional(Type.Boolean()),
        typeIsFixedLine: Type.Optional(Type.Boolean()),
      },
      { additionalProperties: true }
    )
  ),
  soleProp: Type.Optional(Type.String()),
  phones: Type.Optional(
    Type.Array(
      Type.Object(
        {
          number: Type.Optional(
            Type.Object({
              input: Type.Optional(Type.String()),
              international: Type.Optional(Type.String()),
              national: Type.Optional(Type.String()),
              e164: Type.Optional(Type.String()),
              rfc3966: Type.Optional(Type.String()),
              significant: Type.Optional(Type.String()),
            })
          ),
          regionCode: Type.Optional(Type.String()),
          valid: Type.Optional(Type.Boolean()),
          possible: Type.Optional(Type.Boolean()),
          possibility: Type.Optional(Type.String()),
          countryCode: Type.Optional(Type.Number()),
          canBeInternationallyDialled: Type.Optional(Type.Boolean()),
          typeIsMobile: Type.Optional(Type.Boolean()),
          typeIsFixedLine: Type.Optional(Type.Boolean()),
        },
        { additionalProperties: true }
      )
    )
  ),
  email: Type.Optional(Type.String()),
  npi_date: Type.Optional(Type.Any()),
  npi_update: Type.Optional(Type.Any()),
  npi_status: Type.Optional(Type.String()),
  npi: Type.Optional(Type.String()),
  license: Type.Optional(Type.String()),
  licenses: Type.Optional(
    Type.Array(
      Type.Object(
        { state: Type.Optional(Type.String()) },
        { additionalProperties: true }
      )
    )
  ),
  cities: Type.Optional(
    Type.Array(
      Type.Object({
        city: Type.Optional(Type.String()),
        state: Type.Optional(Type.String()),
      })
    )
  ),
  license_states: Type.Optional(Type.Array(Type.String())),
  taxonomy1: Type.Optional(Type.String()),
  taxonomy2: Type.Optional(Type.String()),
  taxonomy3: Type.Optional(Type.String()),
  providers: Type.Optional(Type.Record(Type.String(), Type.Object({
    id: ObjectIdSchema()
  }))),
  ...commonFields
}, { $id: 'Practitioners', additionalProperties: false })

export type Practitioners = Static<typeof practitionersSchema>
export const practitionersValidator = getValidator(practitionersSchema, dataValidator)

// Email handler for resolvers
const emailHandler = async (val: string | undefined) => {
  if (val) return val.toLowerCase().trim()
  return val
}

export const practitionersResolver = resolve<Practitioners, HookContext>({
  name: async (val, data) => {
    return data.firstName + ' ' + data.lastName
  },
  email: emailHandler,
  gender: async (val) => {
    if (val === 'M') return 'male'
    if (val === 'F') return 'female'
    return val
  }
})

export const practitionersExternalResolver = resolve<Practitioners, HookContext>({})

// Schema for creating new data
export const practitionersDataSchema = Type.Object({
  ...Type.Omit(practitionersSchema, ['_id']).properties
}, { additionalProperties: false })

export type PractitionersData = Static<typeof practitionersDataSchema>
export const practitionersDataValidator = getValidator(practitionersDataSchema, dataValidator)
export const practitionersDataResolver = resolve<PractitionersData, HookContext>({
  npi: async (val, data) => {
    if (!val) return `*_${data.lastName}_${new Date().getTime()}`
    return val
  }
})

// Pick ObjectId fields and nested ObjectId fields for query properties
const practitionersQueryProperties = Type.Pick(practitionersSchema, ['_id', 'person'])

export const practitionersPatchSchema = commonPatch(practitionersSchema, { pushPullOpts: [], pickedForSet: practitionersQueryProperties })

export type PractitionersPatch = Static<typeof practitionersPatchSchema>
export const practitionersPatchValidator = getValidator(practitionersPatchSchema, dataValidator)
export const practitionersPatchResolver = resolve<PractitionersPatch, HookContext>({})

export const practitionersQuerySchema = Type.Intersect([
  queryWrapper(practitionersQueryProperties),
  Type.Object({
    name: Type.Optional(Type.Any()),
    firstName: Type.Optional(Type.Any()),
    lastName: Type.Optional(Type.Any())
  }, { additionalProperties: false })
])

export type PractitionersQuery = Static<typeof practitionersQuerySchema>
export const practitionersQueryValidator = getValidator(practitionersQuerySchema, queryValidator)
export const practitionersQueryResolver = resolve<PractitionersQuery, HookContext>({})
