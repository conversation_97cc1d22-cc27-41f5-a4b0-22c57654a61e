// TypeBox schema for networks service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, imageSchema, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const networksSchema = Type.Object({
  _id: ObjectIdSchema(),
  avatar: Type.Optional(imageSchema),
  access: Type.Optional(
    Type.Union([
      Type.Literal("public"),
      Type.Literal("private"),
      Type.Literal("searchable"),
    ])
  ),
  subs: Type.Optional(Type.Array(ObjectIdSchema())),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  writers: Type.Optional(Type.Array(ObjectIdSchema())),
  images: Type.Optional(imageSchema),
  name: Type.String(),
  description: Type.String(),
  lastSync: Type.Optional(Type.String()),
  bundle_changes: Type.Optional(Type.Array(ObjectIdSchema())),
  plans: Type.Optional(Type.Array(ObjectIdSchema())),
  bundles: Type.Optional(Type.Array(ObjectIdSchema())),
  bundle_reqs: Type.Optional(Type.Array(ObjectIdSchema())),
  plan_reqs: Type.Optional(Type.Array(ObjectIdSchema())),
  bundle_invites: Type.Optional(Type.Array(ObjectIdSchema())),
  plans_invites: Type.Optional(Type.Array(ObjectIdSchema())),
  ...commonFields
}, { $id: "Networks", additionalProperties: false })

export type Networks = Static<typeof networksSchema>
export const networksValidator = getValidator(networksSchema, dataValidator)
export const networksResolver = resolve<Networks, HookContext>({})
export const networksExternalResolver = resolve<Networks, HookContext>({})

export const networksDataSchema = Type.Object({
  ...Type.Omit(networksSchema, ['_id']).properties
}, { additionalProperties: false })

export type NetworksData = Static<typeof networksDataSchema>
export const networksDataValidator = getValidator(networksDataSchema, dataValidator)
export const networksDataResolver = resolve<NetworksData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const networksQueryProperties = Type.Pick(networksSchema, ['_id', 'subs', 'managers', 'writers', 'bundle_changes', 'plans', 'bundles', 'bundle_reqs', 'plan_reqs', 'bundle_invites', 'plans_invites', 'createdBy', 'updatedBy', 'env', 'host', 'ref', 'changeLog'])

export const networksPatchSchema = commonPatch(networksSchema, { pushPullOpts: [], pickedForSet: networksQueryProperties })
export type NetworksPatch = Static<typeof networksPatchSchema>
export const networksPatchValidator = getValidator(networksPatchSchema, dataValidator)
export const networksPatchResolver = resolve<NetworksPatch, HookContext>({})

export const networksQuerySchema = queryWrapper(networksQueryProperties)
export type NetworksQuery = Static<typeof networksQuerySchema>
export const networksQueryValidator = getValidator(networksQuerySchema, queryValidator)
export const networksQueryResolver = resolve<NetworksQuery, HookContext>({})
