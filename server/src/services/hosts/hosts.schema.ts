// TypeBox schema for hosts service
import {resolve} from '@feathersjs/schema'
import {Type, getValidator, ObjectIdSchema} from '@feathersjs/typebox'
import type {Static} from '@feathersjs/typebox'
import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch, queryWrapper, phoneSchema, addressSchema, imageSchema, videoSchema} from '../../utils/common/typebox-schemas.js'

import { guideEnum } from './schemas/enums.js';

export const hostsSchema = Type.Object(
    {
        // required at root
        _id: ObjectIdSchema(),
        org: ObjectIdSchema(),
        dba: Type.String(),

        // optional
        appDefault: Type.Optional(Type.Boolean()),
        description: Type.Optional(Type.String()),
        avatar: Type.Optional(imageSchema),
        subdomain: Type.Optional(Type.String()),
        allVideos: Type.Optional(Type.Array(ObjectIdSchema())), // uploadIds
        phones: Type.Optional(Type.Array(phoneSchema)),
        emails: Type.Optional(Type.Array(Type.String())),
        locations: Type.Optional(Type.Array(addressSchema)),
        refs: Type.Optional(Type.Array(ObjectIdSchema())),
        teams: Type.Optional(Type.Array(ObjectIdSchema())),
        npn: Type.Optional(Type.String()),

        shopStatuses: Type.Optional(
            Type.Record(
                Type.String(),
                Type.Object(
                    {
                        label: Type.Optional(Type.String()),
                        color: Type.Optional(Type.String()),
                    },
                    { additionalProperties: true }
                )
            )
        ),

        publicSupport: Type.Optional(ObjectIdSchema()), // team

        plans: Type.Optional(
            Type.Record(
                Type.String(),
                Type.Object(
                    {
                        team: Type.Optional(ObjectIdSchema()),
                        payContract: Type.Optional(ObjectIdSchema()),
                    },
                    { additionalProperties: true }
                )
            )
        ),

        videos: Type.Optional(
            Type.Object(
                {
                    intro: Type.Optional(
                        Type.Record(Type.String(), videoSchema)
                    ),
                },
                { additionalProperties: true }
            )
        ),

        states: Type.Optional(
            Type.Record(
                Type.String({ pattern: '[A-Z]{2}' }), // state code: AK, UT, etc.
                Type.Object(
                    {
                        state: Type.Optional(Type.String()),
                        counties: Type.Optional(
                            Type.Record(
                                Type.String(),
                                Type.Object(
                                    {
                                        all: Type.Optional(Type.Boolean()),
                                        cities: Type.Optional(Type.Array(Type.String())),
                                    },
                                    { additionalProperties: true }
                                )
                            )
                        ),
                        all: Type.Optional(Type.Boolean()),
                    },
                    { additionalProperties: true }
                )
            )
        ),

        roles: Type.Optional(
            Type.Array(Type.String({ enum: guideEnum }))
        ),

        broker: Type.Optional(
            Type.Object(
                {
                    active: Type.Optional(Type.Boolean()),
                    ichra: Type.Optional(Type.Boolean()),
                },
                { additionalProperties: true }
            )
        ),

        // spread common fields as-is (keeps their optional/required settings)
        ...commonFields,
    },
    {
        $id: 'Hosts',
        additionalProperties: false,
        required: ['_id', 'org', 'dba'],
    }
);

export type Hosts = Static<typeof hostsSchema>
export const hostsValidator = getValidator(hostsSchema, dataValidator)
export const hostsResolver = resolve<Hosts, HookContext>({})
export const hostsExternalResolver = resolve<Hosts, HookContext>({})

export const hostsDataSchema = Type.Object({
    ...Type.Omit(hostsSchema, ['_id']).properties
}, {additionalProperties: false})

export type HostsData = Static<typeof hostsDataSchema>
export const hostsDataValidator = getValidator(hostsDataSchema, dataValidator)
export const hostsDataResolver = resolve<HostsData, HookContext>({})
const hostsQueryProperties = Type.Pick(hostsSchema, ['_id', 'plans', 'refs', 'teams', 'org', 'publicSupport'])

export const hostsPatchSchema = commonPatch(hostsSchema, {
    pushPullOpts: [
        {path: 'allVideos', type: ObjectIdSchema()},
        {path: 'phones', type: phoneSchema},
        {path: 'locations', type: addressSchema},
        {path: 'emails', type: {type: 'string'}}
    ]
    , pickedForSet: hostsQueryProperties
})
export type HostsPatch = Static<typeof hostsPatchSchema>
export const hostsPatchValidator = getValidator(hostsPatchSchema, dataValidator)
export const hostsPatchResolver = resolve<HostsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility

export const hostsQuerySchema = queryWrapper(hostsQueryProperties)
export type HostsQuery = Static<typeof hostsQuerySchema>
export const hostsQueryValidator = getValidator(hostsQuerySchema, queryValidator)
export const hostsQueryResolver = resolve<HostsQuery, HookContext>({})
