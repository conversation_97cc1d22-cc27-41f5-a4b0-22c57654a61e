// TypeBox schema for prices service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

export const pricesSchema = Type.Object({
  _id: ObjectIdSchema(),
  provider: Type.Optional(ObjectIdSchema()),
  bundle: Type.Optional(ObjectIdSchema()),
  source: Type.Optional(
    Type.Union([
      Type.Literal("vision"),
      Type.Literal("bill"),
      Type.Literal("provider"),
      Type.Literal("dataset"),
      Type.Literal("va"),
      Type.Literal("upload"),
      Type.Literal("ptf"),
    ])
  ),
  description: Type.Optional(Type.String()),
  notes: Type.Optional(Type.String()),
  session: Type.Optional(Type.String()),
  state: Type.Optional(Type.String()),
  eraser: Type.Optional(ObjectIdSchema()),
  name: Type.Optional(Type.String()),
  providerName: Type.Optional(Type.String()),
  price: Type.Optional(Type.Number()),
  uom: Type.Optional(Type.String()),
  batch: Type.Optional(Type.String()),
  uid: Type.Optional(Type.String()),
  subject: Type.Optional(ObjectIdSchema()),
  type: Type.Optional(
    Type.Union([
      Type.Literal("procedures"),
      Type.Literal("meds"),
      Type.Literal("other"),
    ])
  ),
  code: Type.Optional(Type.String()),
  billing_code: Type.Optional(Type.String()),
  carrier: Type.Optional(Type.String()),
  ndcs: Type.Optional(Type.Array(Type.String())),
  relatedCheckedAt: Type.Optional(Type.Any()),
  ndc: Type.Optional(Type.String()),
  ndc10: Type.Optional(Type.String()),
  ndc11: Type.Optional(Type.String()),
  labeler: Type.Optional(Type.String()),
  product: Type.Optional(Type.String()),
  package: Type.Optional(Type.String()),
  rxcui: Type.Optional(Type.String()),
  s_f: Type.Optional(Type.String()),
  ...commonFields
}, { $id: 'Prices', additionalProperties: false })

export type Prices = Static<typeof pricesSchema>
export const pricesValidator = getValidator(pricesSchema, dataValidator)
export const pricesResolver = resolve<Prices, HookContext>({})
export const pricesExternalResolver = resolve<Prices, HookContext>({})

export const pricesDataSchema = Type.Object({
  ...Type.Omit(pricesSchema, ['_id']).properties
}, { additionalProperties: false })

export type PricesData = Static<typeof pricesDataSchema>
export const pricesDataValidator = getValidator(pricesDataSchema, dataValidator)
export const pricesDataResolver = resolve<PricesData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const pricesQueryProperties = Type.Pick(pricesSchema, ['_id', 'provider', 'bundle', 'eraser', 'subject'])

export const pricesPatchSchema = commonPatch(pricesSchema, { pushPullOpts: [], pickedForSet: pricesQueryProperties })
export type PricesPatch = Static<typeof pricesPatchSchema>
export const pricesPatchValidator = getValidator(pricesPatchSchema, dataValidator)
export const pricesPatchResolver = resolve<PricesPatch, HookContext>({})

// Allow querying on any field from the main schema
export const pricesQuerySchema = queryWrapper(pricesQueryProperties)
export type PricesQuery = Static<typeof pricesQuerySchema>
export const pricesQueryValidator = getValidator(pricesQuerySchema, queryValidator)
export const pricesQueryResolver = resolve<PricesQuery, HookContext>({})
