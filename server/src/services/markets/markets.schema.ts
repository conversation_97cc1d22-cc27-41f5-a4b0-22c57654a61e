// TypeBox schema for markets service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, queryWrapper, geoJsonSchema } from '../../utils/common/typebox-schemas.js'

export const marketsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  hosts: Type.Optional(Type.Array(ObjectIdSchema())),
  owners: Type.Optional(Type.Array(ObjectIdSchema())),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  geo: Type.Optional(geoJsonSchema),
  locations: Type.Optional(
    Type.Record(Type.String(), Type.Object({
      cities: Type.Optional(Type.Array(Type.String())),
      zips: Type.Optional(Type.Array(Type.String())),
      counties: Type.Optional(Type.Array(Type.String()))
    }))
  ),
  ...commonFields
}, { $id: "Markets", additionalProperties: false })

export type Markets = Static<typeof marketsSchema>
export const marketsValidator = getValidator(marketsSchema, dataValidator)
export const marketsResolver = resolve<Markets, HookContext>({})
export const marketsExternalResolver = resolve<Markets, HookContext>({})

export const marketsDataSchema = Type.Object({
  ...Type.Omit(marketsSchema, ['_id']).properties
}, { additionalProperties: false })

export type MarketsData = Static<typeof marketsDataSchema>
export const marketsDataValidator = getValidator(marketsDataSchema, dataValidator)
export const marketsDataResolver = resolve<MarketsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const marketsQueryProperties = Type.Pick(marketsSchema, ['_id', 'hosts', 'owners', 'managers', 'createdBy', 'updatedBy', 'env', 'host', 'ref', 'changeLog'])

export const marketsPatchSchema = commonPatch(marketsSchema, { pushPullOpts: [], pickedForSet: marketsQueryProperties })
export type MarketsPatch = Static<typeof marketsPatchSchema>
export const marketsPatchValidator = getValidator(marketsPatchSchema, dataValidator)
export const marketsPatchResolver = resolve<MarketsPatch, HookContext>({})

export const marketsQuerySchema = queryWrapper(marketsQueryProperties)
export type MarketsQuery = Static<typeof marketsQuerySchema>
export const marketsQueryValidator = getValidator(marketsQuerySchema, queryValidator)
export const marketsQueryResolver = resolve<MarketsQuery, HookContext>({})
