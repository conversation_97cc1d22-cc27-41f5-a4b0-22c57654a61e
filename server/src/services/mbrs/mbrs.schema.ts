// TypeBox schema for mbrs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const mbrsSchema = Type.Object({
  _id: ObjectIdSchema(),
  coverage: ObjectIdSchema(),
  person: ObjectIdSchema(),
  enrollment: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  provider: Type.Optional(ObjectIdSchema()),
  pm: Type.Optional(ObjectIdSchema()),
  inactive: Type.Optional(Type.Boolean()),
  itemId: Type.String(),
  ...commonFields
}, { $id: "Mbrs", additionalProperties: false })

export type Mbrs = Static<typeof mbrsSchema>
export const mbrsValidator = getValidator(mbrsSchema, dataValidator)
export const mbrsResolver = resolve<Mbrs, HookContext>({})
export const mbrsExternalResolver = resolve<Mbrs, HookContext>({})

// Schema for creating new data
export const mbrsDataSchema = Type.Object({
  ...Type.Omit(mbrsSchema, ['_id']).properties
}, { additionalProperties: false })

export type MbrsData = Static<typeof mbrsDataSchema>
export const mbrsDataValidator = getValidator(mbrsDataSchema, dataValidator)
export const mbrsDataResolver = resolve<MbrsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const mbrsQueryProperties = Type.Pick(mbrsSchema, ['_id', 'coverage', 'person', 'enrollment', 'plan', 'provider', 'pm', 'createdBy', 'updatedBy', 'env', 'host', 'ref', 'changeLog'])

// Schema for updating existing data
export const mbrsPatchSchema = commonPatch(mbrsSchema, { pushPullOpts: [], pickedForSet: mbrsQueryProperties })

export type MbrsPatch = Static<typeof mbrsPatchSchema>
export const mbrsPatchValidator = getValidator(mbrsPatchSchema, dataValidator)
export const mbrsPatchResolver = resolve<MbrsPatch, HookContext>({})

export const mbrsQuerySchema = queryWrapper(mbrsQueryProperties)

export type MbrsQuery = Static<typeof mbrsQuerySchema>
export const mbrsQueryValidator = getValidator(mbrsQuerySchema, queryValidator)
export const mbrsQueryResolver = resolve<MbrsQuery, HookContext>({})
