// TypeBox schema for ppls service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, phoneSchema, imageSchema, addressSchema, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const pplsSchema = Type.Object(
    {
      _id: Type.Optional(ObjectIdSchema()),
      name: Type.Optional(Type.String()),
      lastName: Type.Optional(Type.String()),
      firstName: Type.Optional(Type.String()),
      did: Type.Optional(Type.String()),
      fid: Type.Optional(Type.String()),
      wallet: Type.Optional(ObjectIdSchema()),
      address: Type.Optional(addressSchema),
      addresses: Type.Optional(Type.Array(addressSchema)),

      email: Type.Optional(Type.Union([Type.String(), Type.Null()])),
      emails: Type.Optional(Type.Array(Type.String())),

      phone: Type.Optional(Type.Union([Type.Null(), phoneSchema])),
      phones: Type.Optional(Type.Array(phoneSchema)),

      household: Type.Optional(ObjectIdSchema()),
      login: Type.Optional(ObjectIdSchema()),
      avatar: Type.Optional(imageSchema),

      online: Type.Optional(Type.Boolean()),
      onlineAt: Type.Optional(Type.Any()),

      ramp_user_id: Type.Optional(Type.String()),
      moovCardholderId: Type.Optional(Type.String()),
      ims: Type.Optional(Type.Array(ObjectIdSchema())),

      moovAccounts: Type.Optional(
          Type.Record(
              Type.String(),
              Type.Object(
                  {
                    id: Type.Optional(Type.String()),
                    isController: Type.Optional(Type.Boolean()),
                  },
                  { additionalProperties: true }
              )
          )
      ),

      cams: Type.Optional(Type.Array(ObjectIdSchema())),
      inGroups: Type.Optional(Type.Array(ObjectIdSchema())),
      inOrgs: Type.Optional(Type.Array(ObjectIdSchema())),
      lastGroupSync: Type.Optional(Type.Any()),
      refs: Type.Optional(Type.Array(ObjectIdSchema())),
      badges: Type.Optional(Type.Array(Type.String())),

      dob: Type.Optional(Type.String()),
      ssn: Type.Optional(Type.String()),
      itin: Type.Optional(Type.String()),
      last4Itin: Type.Optional(Type.String()),
      last4Ssn: Type.Optional(Type.String()),
      gender: Type.Optional(Type.String()),
      cleanupFlag: Type.Optional(Type.Boolean()),
      enrollments: Type.Optional(Type.Array(ObjectIdSchema())),

      invites: Type.Optional(
          Type.Record(
              Type.String(), // org id being invited to
              Type.Object(
                  {
                    by: Type.Optional(ObjectIdSchema()),
                    at: Type.Optional(Type.Any()),
                    reminded: Type.Optional(Type.Array(Type.Any())),
                    caps: Type.Optional(
                        Type.Record(
                            Type.String(),
                            Type.Object(
                                {
                                  id: ObjectIdSchema(),
                                  path: Type.String({ description: 'Path of the caps.cap permission set for this person' })
                                },
                                { additionalProperties: true, required: ['id', 'path'] }
                            )
                        )
                    )
                  },
                  { additionalProperties: true }
              )
          )
      ),

      preferences: Type.Optional(
          Type.Object(
              {
                notifications: Type.Optional(
                    Type.Object(
                        {
                          unsub: Type.Optional(Type.Array(Type.String())),     // categories or '*' for all
                          emailUnsub: Type.Optional(Type.Array(Type.String())),
                          smsUnsub: Type.Optional(Type.Array(Type.String())),
                        },
                        { additionalProperties: true }
                    )
                )
              },
              { additionalProperties: true }
          )
      ),

      card_user: Type.Optional(Type.Array(ObjectIdSchema())),
      budget_user: Type.Optional(Type.Array(ObjectIdSchema())),

      // bring in shared fields as-is
      ...commonFields,
    },
    {
      $id: 'Ppls',
      additionalProperties: false,
      required: [], // none required
    }
);

export type Ppls = Static<typeof pplsSchema>
export const pplsValidator = getValidator(pplsSchema, dataValidator)
export const pplsResolver = resolve<Ppls, HookContext>({})
export const pplsExternalResolver = resolve<Ppls, HookContext>({})

export const pplsDataSchema = Type.Object({
  ...Type.Omit(pplsSchema, ['_id']).properties
}, { additionalProperties: false })

export type PplsData = Static<typeof pplsDataSchema>
export const pplsDataValidator = getValidator(pplsDataSchema, dataValidator)
export const pplsDataResolver = resolve<PplsData, HookContext>({})

const pplsQueryProperties = Type.Pick(pplsSchema, ['_id', 'login', 'household', 'card_user', 'budget_user', 'inOrgs', 'inGroups', 'wallet'])

export const pplsPatchSchema = commonPatch(pplsSchema, {
    pushPullOpts: [],
    pickedForSet: pplsQueryProperties
})

export type PplsPatch = Static<typeof pplsPatchSchema>
export const pplsPatchValidator = getValidator(pplsPatchSchema, dataValidator)
export const pplsPatchResolver = resolve<PplsPatch, HookContext>({})

export const pplsQuerySchema = queryWrapper(pplsQueryProperties)
export type PplsQuery = Static<typeof pplsQuerySchema>
export const pplsQueryValidator = getValidator(pplsQuerySchema, queryValidator)
export const pplsQueryResolver = resolve<PplsQuery, HookContext>({})
