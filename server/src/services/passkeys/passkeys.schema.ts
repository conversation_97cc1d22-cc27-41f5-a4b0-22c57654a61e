// TypeBox schema for passkeys service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, queryWrapper } from '../../utils/common/typebox-schemas.js'


export const passkeysSchema = Type.Object({
  _id: ObjectIdSchema(),
  login: ObjectIdSchema(),
  rpID: Type.String({ description: "Relying Party ID, e.g. example.com" }),
  credentialId: Type.String({
    description: "WebAuthnCredential.id (base64url)",
    pattern: "^[A-Za-z0-9_-]+$",
  }),
  publicKey: Type.String({
    description: "WebAuthnCredential.publicKey encoded to base64url",
    pattern: "^[A-Za-z0-9_-]+$",
  }),
  signCount: Type.Number({
    description: "Verification counter (may not always increase)",
  }),
  transports: Type.Optional(
    Type.Array(
      Type.Union([
        Type.Literal("usb"),
        Type.Literal("nfc"),
        Type.Literal("ble"),
        Type.Literal("internal"),
        Type.Literal("hybrid"),
        Type.Literal("cable"),
      ]),
      { description: "Authenticator transport hints" }
    )
  ),
  aaguid: Type.Optional(
    Type.String({
      description: "Authenticator AAGUID (UUID string if provided)",
    })
  ),
  backupEligible: Type.Optional(
    Type.Boolean({
      description: 'Derived from credentialDeviceType === "multiDevice"',
    })
  ),
  backupState: Type.Optional(
    Type.Union([Type.Literal("enabled"), Type.Literal("disabled")], {
      description: "Derived from credentialBackedUp",
    })
  ),
  displayName: Type.Optional(
    Type.String({ description: "User-visible label for the device" })
  ),
  ...commonFields
}, { $id: "Passkeys", additionalProperties: false })

export type Passkeys = Static<typeof passkeysSchema>
export const passkeysValidator = getValidator(passkeysSchema, dataValidator)
export const passkeysResolver = resolve<Passkeys, HookContext>({})
export const passkeysExternalResolver = resolve<Passkeys, HookContext>({})

export const passkeysDataSchema = Type.Object({
  ...Type.Omit(passkeysSchema, ['_id']).properties
}, { additionalProperties: false })

export type PasskeysData = Static<typeof passkeysDataSchema>
export const passkeysDataValidator = getValidator(passkeysDataSchema, dataValidator)
export const passkeysDataResolver = resolve<PasskeysData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const passkeysQueryProperties = Type.Pick(passkeysSchema, ['_id', 'login', 'createdBy', 'updatedBy', 'env', 'host', 'ref', 'changeLog'])

export const passkeysPatchSchema = commonPatch(passkeysSchema, { pushPullOpts: [], pickedForSet: passkeysQueryProperties })
export type PasskeysPatch = Static<typeof passkeysPatchSchema>
export const passkeysPatchValidator = getValidator(passkeysPatchSchema, dataValidator)
export const passkeysPatchResolver = resolve<PasskeysPatch, HookContext>({})

export const passkeysQuerySchema = queryWrapper(passkeysQueryProperties)
export type PasskeysQuery = Static<typeof passkeysQuerySchema>
export const passkeysQueryValidator = getValidator(passkeysQuerySchema, queryValidator)
export const passkeysQueryResolver = resolve<PasskeysQuery, HookContext>({})
