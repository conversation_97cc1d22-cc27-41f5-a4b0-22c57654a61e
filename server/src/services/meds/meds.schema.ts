// TypeBox schema for meds service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, queryWrapper } from '../../utils/common/typebox-schemas.js'

const relatedInfo = Type.Record(
    Type.String(),
    Type.Object(
        {
          rxcui:    Type.Optional(Type.String()),
          name:     Type.Optional(Type.String()),
          synonym:  Type.Optional(Type.String()),
          language: Type.Optional(Type.String()),
          suppress: Type.Optional(Type.String()),
          umlscui:  Type.Optional(Type.String()),
        },
        { additionalProperties: true }
    )
);

// Meds schema
export const medsSchema = Type.Object(
    {
      // required at root
      _id: ObjectIdSchema(),
      rxcui: Type.String(),

      // optional
      standard:       Type.Optional(Type.String()),
      rxcuis:         Type.Optional(Type.Array(Type.String())),
      s_f:            Type.Optional(Type.Array(Type.String())), // strengths and forms
      variants:       Type.Optional(Type.Array(Type.String())),
      name:           Type.Optional(Type.String()),
      medical_name:   Type.Optional(Type.String()),
      consumer_name:  Type.Optional(Type.String()),
      description:    Type.Optional(Type.String()),
      activeIngredient: Type.Optional(Type.String()),
      synonyms:       Type.Optional(Type.String()),
      sbdOf:          Type.Optional(ObjectIdSchema()),
      ndcs:           Type.Optional(Type.Array(Type.String())),

      info: Type.Optional(
          Type.Object(
              {
                IN:    Type.Optional(relatedInfo),
                PIN:   Type.Optional(relatedInfo),
                MIN:   Type.Optional(relatedInfo),
                SCD:   Type.Optional(relatedInfo),
                SCDF:  Type.Optional(relatedInfo),
                SCDG:  Type.Optional(relatedInfo),
                SCDC:  Type.Optional(relatedInfo),
                GPCK:  Type.Optional(relatedInfo),
                BN:    Type.Optional(relatedInfo),
                BPCK:  Type.Optional(relatedInfo),
                DF:    Type.Optional(relatedInfo),
                DFG:   Type.Optional(relatedInfo),
                SBD:   Type.Optional(relatedInfo),
                SBDG:  Type.Optional(relatedInfo),
                SBDC:  Type.Optional(relatedInfo),
                SBDF:  Type.Optional(relatedInfo),
                SBDFP: Type.Optional(relatedInfo),
              },
              { additionalProperties: true }
          )
      ),

      // bring in shared fields as-is
      ...commonFields,
    },
    {
      $id: 'Meds',
      additionalProperties: false,
      required: ['_id', 'rxcui'],
    }
);

export type Meds = Static<typeof medsSchema>
export const medsValidator = getValidator(medsSchema, dataValidator)
export const medsResolver = resolve<Meds, HookContext>({})
export const medsExternalResolver = resolve<Meds, HookContext>({})

export const medsDataSchema = Type.Object({
  ...Type.Omit(medsSchema, ['_id']).properties
}, { additionalProperties: false })

export type MedsData = Static<typeof medsDataSchema>
export const medsDataValidator = getValidator(medsDataSchema, dataValidator)
export const medsDataResolver = resolve<MedsData, HookContext>({})

const medsQueryProperties = Type.Pick(medsSchema, ['_id'])

export const medsPatchSchema = commonPatch(medsSchema, {pushPullOpts: [], pickedForSet: medsQueryProperties})
export type MedsPatch = Static<typeof medsPatchSchema>
export const medsPatchValidator = getValidator(medsPatchSchema, dataValidator)
export const medsPatchResolver = resolve<MedsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility

export const medsQuerySchema = queryWrapper(medsQueryProperties)
export type MedsQuery = Static<typeof medsQuerySchema>
export const medsQueryValidator = getValidator(medsQuerySchema, queryValidator)
export const medsQueryResolver = resolve<MedsQuery, HookContext>({})
