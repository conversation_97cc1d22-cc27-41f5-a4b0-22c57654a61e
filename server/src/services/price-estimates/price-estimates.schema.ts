// TypeBox schema for price-estimates service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, imageSchema, commonPatch } from '../../utils/common/typebox-schemas.js'

export const priceEstimatesSchema = Type.Object({
  _id: ObjectIdSchema(),
  medicare: Type.Optional(Type.Number()),
  cash: Type.Optional(Type.Number()),
  medicare_low: Type.Optional(Type.Number()),
  medicare_high: Type.Optional(Type.Number()),
  description: Type.Optional(Type.String()),
  cash_low: Type.Optional(Type.Number()),
  cash_high: Type.Optional(Type.Number()),
  source: Type.Optional(Type.String()),
  estimatedAt: Type.Optional(Type.Any()),
  alts: Type.Optional(
    Type.Array(
      Type.Object(
        {
          medicare: Type.Optional(Type.Number()),
          cash: Type.Optional(Type.Number()),
          medicare_low: Type.Optional(Type.Number()),
          medicare_high: Type.Optional(Type.Number()),
          description: Type.Optional(Type.String()),
          cash_low: Type.Optional(Type.Number()),
          cash_high: Type.Optional(Type.Number()),
          source: Type.Optional(Type.String()),
          estimatedAt: Type.Optional(Type.Any()),
        },
        { additionalProperties: true }
      )
    )
  ),
  code: Type.String(),
  rxcui: Type.Optional(Type.String()),
  locationCode: Type.String(),
  listPrice: Type.Optional(Type.Number()),
  carrier: Type.Optional(Type.String()),
  files: Type.Optional(imageSchema),
  session: Type.Optional(Type.String()),
  zip_code: Type.Optional(Type.String()),
  state: Type.Optional(Type.String()),
  ...commonFields
}, { $id: 'PriceEstimates', additionalProperties: false })

export type PriceEstimates = Static<typeof priceEstimatesSchema>
export const priceEstimatesValidator = getValidator(priceEstimatesSchema, dataValidator)
export const priceEstimatesResolver = resolve<PriceEstimates, HookContext>({})
export const priceEstimatesExternalResolver = resolve<PriceEstimates, HookContext>({})

export const priceEstimatesDataSchema = Type.Object({
  ...Type.Omit(priceEstimatesSchema, ['_id']).properties
}, { additionalProperties: false })

export type PriceEstimatesData = Static<typeof priceEstimatesDataSchema>
export const priceEstimatesDataValidator = getValidator(priceEstimatesDataSchema, dataValidator)
export const priceEstimatesDataResolver = resolve<PriceEstimatesData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const priceEstimatesQueryProperties = Type.Pick(priceEstimatesSchema, ['_id'])

export const priceEstimatesPatchSchema = commonPatch(priceEstimatesSchema, { pushPullOpts: [], pickedForSet: priceEstimatesQueryProperties })
export type PriceEstimatesPatch = Static<typeof priceEstimatesPatchSchema>
export const priceEstimatesPatchValidator = getValidator(priceEstimatesPatchSchema, dataValidator)
export const priceEstimatesPatchResolver = resolve<PriceEstimatesPatch, HookContext>({})

// Allow querying on any field from the main schema
export const priceEstimatesQuerySchema = queryWrapper(priceEstimatesSchema)
export type PriceEstimatesQuery = Static<typeof priceEstimatesQuerySchema>
export const priceEstimatesQueryValidator = getValidator(priceEstimatesQuerySchema, queryValidator)
export const priceEstimatesQueryResolver = resolve<PriceEstimatesQuery, HookContext>({})

// Export for backward compatibility with business logic
export const estimateBodySchema = Type.Object({
  procedure: Type.Optional(Type.String()),
  provider: Type.Optional(Type.String()),
  location: Type.Optional(Type.String()),
  insurance: Type.Optional(Type.String())
}, { additionalProperties: false })

export const priceKeys = Type.Object({
  amount: Type.Optional(Type.Number()),
  copay: Type.Optional(Type.Number()),
  coinsurance: Type.Optional(Type.Number()),
  deductible: Type.Optional(Type.Number()),
  total: Type.Optional(Type.Number())
}, { additionalProperties: false })
