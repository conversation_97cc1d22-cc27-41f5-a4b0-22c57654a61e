// TypeBox schema for plans service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, imageSchema, commonPatch, queryWrapper } from '../../utils/common/typebox-schemas.js'

import { employerContribution } from './schemas/contributions.js';
import { contributions } from '../enrollments/schemas/contributions.js';

import { feeEnum } from '../offers/schemas/enums.js';
import { guideEnum } from '../hosts/schemas/enums.js';

/** cafe */
const cafe = Type.Object(
    {
      active: Type.Optional(Type.Boolean()),
      doc: Type.Optional(ObjectIdSchema()),
      manual_doc: Type.Optional(ObjectIdSchema()), // upload id
      taxStatus: Type.Optional(
          Type.Union([Type.Literal(0), Type.Literal(1), Type.Literal(2)]) // 0 = taxable, 1 = no tax, 2 = mix
      ),
      hsaEligible: Type.Optional(Type.Boolean()),
      gracePeriod: Type.Optional(Type.Number()), // days
      carryover: Type.Optional(Type.Number()),
      budget: Type.Optional(ObjectIdSchema()),
      monthlySpend: Type.Optional(Type.Number()),
      limits: Type.Optional(
          Type.Object(
              {
                single: Type.Optional(Type.Number()),
                family: Type.Optional(Type.Number()),
              },
              { additionalProperties: true }
          )
      ),
    },
    { additionalProperties: true }
);

/** cafeSchema */
const cafeSchema = Type.Object(
    {
      hsa: Type.Optional(cafe),
      fsa: Type.Optional(cafe),
      dcp: Type.Optional(cafe),
      pop: Type.Optional(cafe),
      def: Type.Optional(cafe), // retirement
      cash: Type.Optional(cafe),
    },
    { additionalProperties: true }
);

/** hra */
const hra = Type.Object(
    {
      active: Type.Optional(Type.Boolean()),
      doc: Type.Optional(ObjectIdSchema()),
      manual_doc: Type.Optional(ObjectIdSchema()), // upload id
      budget: Type.Optional(ObjectIdSchema()),
      recurs: Type.Optional(Type.Number()),
      limits: Type.Optional(
          Type.Object(
              {
                single: Type.Optional(Type.Number()),
                family: Type.Optional(Type.Number()),
              },
              { additionalProperties: true }
          )
      ),
    },
    { additionalProperties: true }
);

/** hraSchema */
const hraSchema = Type.Object(
    {
      ichra: Type.Optional(hra),
      ebhra: Type.Optional(hra),
      gchra: Type.Optional(hra),
    },
    { additionalProperties: true }
);

/** contactInfo */
const contactInfo = Type.Object(
    {
      name: Type.Optional(Type.String()),
      phone: Type.Optional(Type.String()),
      email: Type.Optional(Type.String()),
    },
    { additionalProperties: true }
);

/** rfpSchema */
const rfpSchema = Type.Object(
    {
      hosts: Type.Optional(Type.Array(ObjectIdSchema())),
      public: Type.Optional(Type.Boolean()),
      fee: Type.Optional(Type.Number()),
      feeType: Type.Optional(Type.String({ enum: feeEnum })),
    },
    { additionalProperties: true }
);


/** plansSchema */
export const plansSchema = Type.Object(
    {
      // required
      _id: ObjectIdSchema(),
      name: Type.String(),

      // optional
      org: Type.Optional(ObjectIdSchema()),
      parent: Type.Optional(ObjectIdSchema()),
      doc: Type.Optional(ObjectIdSchema()),
      spd: Type.Optional(ObjectIdSchema()),
      ale: Type.Optional(Type.Boolean()),
      estFte: Type.Optional(Type.Number()),
      groups: Type.Optional(Type.Array(ObjectIdSchema())),
      orgs: Type.Optional(Type.Array(ObjectIdSchema())),

      vectorStoreIds: Type.Optional(
          Type.Record(
              Type.String(),
              Type.Object(
                  {
                    id: Type.Optional(Type.String()), // Vector store id
                    fileIds: Type.Optional(Type.Array(Type.String())), // file ids to remove on update
                    updatedAt: Type.Optional(Type.Any()),
                  },
                  { additionalProperties: true }
              )
          )
      ),

      rfp: Type.Optional(
          Type.Object(
              {
                care_director: Type.Optional(rfpSchema),
                plan_guide: Type.Optional(rfpSchema),
                compliance: Type.Optional(rfpSchema),
                finance: Type.Optional(rfpSchema),
                physician: Type.Optional(rfpSchema),
              },
              { additionalProperties: true }
          )
      ),

      dependents: Type.Optional(
          Type.Object(
              {
                excludeSpouse: Type.Optional(Type.Boolean()),
              },
              { additionalProperties: true }
          )
      ),

      info: Type.Optional(
          Type.Object(
              {
                sponsor: Type.Optional(
                    Type.Object(
                        {
                          ...(contactInfo as any).properties,
                          ein: Type.Optional(Type.String()),
                        },
                        { additionalProperties: true }
                    )
                ),
                planAdmin: Type.Optional(contactInfo),
                fiduciary: Type.Optional(contactInfo),
                legalAgent: Type.Optional(contactInfo),
                number: Type.Optional(Type.String()),
                numberEin: Type.Optional(Type.String()),
              },
              { additionalProperties: true }
          )
      ),

      team: Type.Optional(
          Type.Record(
              Type.String(),
              Type.Object(
                  {
                    id: Type.Optional(ObjectIdSchema()),
                    status: Type.Optional(Type.String({ enum: ['pending', 'canceled', 'active'] })),
                    role: Type.Optional(Type.String({ enum: guideEnum as any })),
                    roleDescription: Type.Optional(Type.String()),
                    fee: Type.Optional(Type.Number()),
                    feeType: Type.Optional(Type.String({ enum: ['alg', 'pepm', 'pmpm', 'flat'] })),
                    feeDescription: Type.Optional(Type.String()),
                    contract: Type.Optional(ObjectIdSchema()),
                    conflicts: Type.Optional(Type.String()),
                    approvedBy: Type.Optional(ObjectIdSchema()),
                    approvedAt: Type.Optional(Type.Any()),
                  },
                  { additionalProperties: true }
              )
          )
      ),

      template: Type.Optional(Type.Boolean()),
      aka: Type.Optional(Type.Array(Type.String())),
      active: Type.Optional(Type.Boolean()),
      description: Type.Optional(Type.String()),

      eligibility: Type.Optional(
          Type.Object(
              {
                hours: Type.Optional(Type.Number()),
                term: Type.Optional(Type.Number()),
              },
              { additionalProperties: true }
          )
      ),

      enrollments: Type.Optional(
          Type.Record(
              Type.String(),
              Type.Object(
                  {
                    active: Type.Optional(Type.Boolean()),
                    description: Type.Optional(Type.String()),
                    open: Type.Optional(Type.String()),
                    close: Type.Optional(Type.String()),
                    enrolled: Type.Optional(Type.Number()),
                    lastUpdate: Type.Optional(Type.Any()),
                    lastEnrolled: Type.Optional(Type.String()),
                    contributions: Type.Optional(contributions),
                    open_enroll: Type.Optional(Type.Boolean()),
                    ppls: Type.Optional(Type.Array(ObjectIdSchema())),
                    groups: Type.Optional(Type.Array(ObjectIdSchema())),
                    sentThrough: Type.Optional(Type.Number()),
                  },
                  { additionalProperties: true }
              )
          )
      ),

      billEraser: Type.Optional(
          Type.Object(
              {
                max: Type.Optional(Type.Number()),
                budget: Type.Optional(ObjectIdSchema()),
              },
              { additionalProperties: true }
          )
      ),

      planYearStart: Type.Optional(Type.Any()),
      cafe: Type.Optional(cafeSchema),
      hra: Type.Optional(hraSchema),

      coverages: Type.Optional(
          Type.Record(
              Type.String(),
              Type.Object(
                  {
                    type: Type.Optional(Type.String()),
                    groups: Type.Optional(Type.Array(ObjectIdSchema())),
                    budget: Type.Optional(ObjectIdSchema()),
                    card: Type.Optional(ObjectIdSchema()),
                    employeeBudget: Type.Optional(Type.Boolean()),
                    employerContribution: Type.Optional(
                        Type.Object(
                            {
                              single: Type.Optional(Type.Number()), // if percent, whole number (e.g., percentage * 100)
                              family: Type.Optional(Type.Number()),
                              type: Type.Optional(Type.String({ enum: ['flat', 'percent'] })),
                            },
                            { additionalProperties: true }
                        )
                    ),
                    id: Type.Optional(ObjectIdSchema()),
                  },
                  { additionalProperties: true }
              )
          )
      ),

      employerContribution: Type.Optional(
          Type.Record(Type.String(), employerContribution)
      ),

      files: Type.Optional(Type.Record(Type.String(), imageSchema)),
      networks: Type.Optional(Type.Array(ObjectIdSchema())),

      // shared fields from your common schema (keep their own required/optional settings)
      ...commonFields,
    },
    {
      $id: 'Plans',
      additionalProperties: false,
      required: ['_id', 'name'],
    }
);

export type Plans = Static<typeof plansSchema>
export const plansValidator = getValidator(plansSchema, dataValidator)
export const plansResolver = resolve<Plans, HookContext>({})
export const plansExternalResolver = resolve<Plans, HookContext>({})

// Schema for creating new data
export const plansDataSchema = Type.Object({
  ...Type.Omit(plansSchema, ['_id']).properties
}, { additionalProperties: false })

export type PlansData = Static<typeof plansDataSchema>
export const plansDataValidator = getValidator(plansDataSchema, dataValidator)
export const plansDataResolver = resolve<PlansData, HookContext>({})

const plansQueryProperties = Type.Pick(plansSchema, ['_id', 'org', 'parent', 'groups', 'orgs', 'team'])
// Schema for updating existing data
export const plansPatchSchema = commonPatch(plansSchema, {
    pushPullOpts: [
        {path: 'groups', type: ObjectIdSchema()},
        {path: 'networks', type: ObjectIdSchema()},
        {path: 'rfp.plan_guide.hosts', type: ObjectIdSchema()},
        {path: 'rfp.care_director.hosts', type: ObjectIdSchema()},
        {path: 'rfp.compliance.hosts', type: ObjectIdSchema()},
        {path: 'rfp.finance.hosts', type: ObjectIdSchema()},
        {path: 'rfp.physician.hosts', type: ObjectIdSchema()},
    ],
    pickedForSet: plansQueryProperties
})
export type PlansPatch = Static<typeof plansPatchSchema>
export const plansPatchValidator = getValidator(plansPatchSchema, dataValidator)
export const plansPatchResolver = resolve<PlansPatch, HookContext>({})



export const plansQuerySchema = queryWrapper(plansQueryProperties)

export type PlansQuery = Static<typeof plansQuerySchema>
export const plansQueryValidator = getValidator(plansQuerySchema, queryValidator)
export const plansQueryResolver = resolve<PlansQuery, HookContext>({})
