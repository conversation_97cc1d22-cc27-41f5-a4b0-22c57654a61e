// TypeBox schema for plan-docs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const planDocsSchema = Type.Object({
  _id: ObjectIdSchema(),
  plan: ObjectIdSchema(),
  smb: Type.Optional(Type.Boolean()),
  public: Type.Optional(Type.Boolean()),
  printCount: Type.Optional(Type.Number()),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  class: Type.Optional(
    Type.Union([
      Type.Literal("core"),
      Type.Literal("125"),
      Type.Literal("105"),
      Type.Literal("misc"),
      Type.Literal("spd"),
    ])
  ),
  subClass: Type.Optional(Type.String()),
  path: Type.Optional(Type.String()),
  sectionsUpdatedAt: Type.Optional(Type.Any()),
  template: Type.Optional(ObjectIdSchema()),
  sections: Type.Optional(
    Type.Record(Type.String(), Type.Object({
      key: Type.Optional(Type.String()),
      title: Type.Optional(Type.String()),
      sections: Type.Optional(
        Type.Record(Type.String(), Type.Object({
          key: Type.Optional(Type.String()),
          title: Type.Optional(Type.String()),
          body: Type.Optional(Type.String())
        }))
      )
    }))
  ),
  ...commonFields
}, { $id: "PlanDocs", additionalProperties: false })

export type PlanDocs = Static<typeof planDocsSchema>
export const planDocsValidator = getValidator(planDocsSchema, dataValidator)
export const planDocsResolver = resolve<PlanDocs, HookContext>({})
export const planDocsExternalResolver = resolve<PlanDocs, HookContext>({})

export const planDocsDataSchema = Type.Object({
  ...Type.Omit(planDocsSchema, ['_id']).properties
}, { additionalProperties: false })

export type PlanDocsData = Static<typeof planDocsDataSchema>
export const planDocsDataValidator = getValidator(planDocsDataSchema, dataValidator)
export const planDocsDataResolver = resolve<PlanDocsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const planDocsQueryProperties = Type.Pick(planDocsSchema, ['_id', 'plan', 'template', 'createdBy', 'updatedBy', 'env', 'host', 'ref', 'changeLog'])

export const planDocsPatchSchema = commonPatch(planDocsSchema, { pushPullOpts: [], pickedForSet: planDocsQueryProperties })
export type PlanDocsPatch = Static<typeof planDocsPatchSchema>
export const planDocsPatchValidator = getValidator(planDocsPatchSchema, dataValidator)
export const planDocsPatchResolver = resolve<PlanDocsPatch, HookContext>({})

export const planDocsQuerySchema = queryWrapper(planDocsQueryProperties)
export type PlanDocsQuery = Static<typeof planDocsQuerySchema>
export const planDocsQueryValidator = getValidator(planDocsQuerySchema, queryValidator)
export const planDocsQueryResolver = resolve<PlanDocsQuery, HookContext>({})
