// TypeBox schema for offers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, queryWrapper } from '../../utils/common/typebox-schemas.js'
import { feeEnum } from './schemas/enums.js'
export const offersSchema = Type.Object({
  _id: ObjectIdSchema(),
  plan: ObjectIdSchema(),
  contract: Type.Optional(ObjectIdSchema()),
  role: Type.Optional(
    Type.Union([
      Type.Literal("care_director"),
      Type.Literal("plan_guide"),
      Type.Literal("compliance"),
      Type.Literal("finance"),
      Type.Literal("physician"),
    ])
  ),
  fee: Type.Number(),
  feeType: Type.String({ enum: feeEnum }),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  status: Type.Optional(
    Type.Union([
      Type.Literal("pending"),
      Type.Literal("rejected"),
      Type.Literal("active"),
    ])
  ),
  ...commonFields
}, { $id: "Offers", additionalProperties: false })


export type Offers = Static<typeof offersSchema>
export const offersValidator = getValidator(offersSchema, dataValidator)
export const offersResolver = resolve<Offers, HookContext>({})
export const offersExternalResolver = resolve<Offers, HookContext>({})

export const offersDataSchema = Type.Object({
  ...Type.Omit(offersSchema, ['_id']).properties
}, { additionalProperties: false })

export type OffersData = Static<typeof offersDataSchema>
export const offersDataValidator = getValidator(offersDataSchema, dataValidator)
export const offersDataResolver = resolve<OffersData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const offersQueryProperties = Type.Pick(offersSchema, ['_id', 'plan', 'contract', 'threads', 'createdBy', 'updatedBy', 'env', 'host', 'ref', 'changeLog'])

export const offersPatchSchema = commonPatch(offersSchema, { pushPullOpts: [], pickedForSet: offersQueryProperties })
export type OffersPatch = Static<typeof offersPatchSchema>
export const offersPatchValidator = getValidator(offersPatchSchema, dataValidator)
export const offersPatchResolver = resolve<OffersPatch, HookContext>({})

export const offersQuerySchema = queryWrapper(offersQueryProperties)
export type OffersQuery = Static<typeof offersQuerySchema>
export const offersQueryValidator = getValidator(offersQuerySchema, queryValidator)
export const offersQueryResolver = resolve<OffersQuery, HookContext>({})
